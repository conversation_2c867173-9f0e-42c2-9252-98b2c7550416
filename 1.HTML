<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>السيرة الذاتية | علي خالد عويد خالد</title>
    <!-- إضافة مكتبة html2canvas لتحويل السيرة الذاتية إلى صورة -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <style>
        /* استيراد الخطوط العربية */
        @import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Noto+Sans+Arabic:wght@300;400;500;700&display=swap');
        
        :root {
            --primary-color: #16213e;
            --secondary-color: #0f3460;
            --accent-color: #4361ee;
            --text-color: #333;
            --light-color: #f5f5f5;
            --border-color: #ddd;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans Arabic', 'Amiri', sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: #f9f9f9;
            direction: rtl;
        }
        
        .cv-container {
            max-width: 800px;
            margin: 20px auto;
            background: white;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.2rem;
            margin-bottom: 5px;
            font-weight: 700;
        }
        
        .header h2 {
            font-size: 1.3rem;
            font-weight: 400;
            opacity: 0.9;
        }
        
        .contact-info {
            background-color: var(--light-color);
            padding: 15px 30px;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--border-color);
        }
        
        .contact-item {
            margin: 5px 0;
            display: flex;
            align-items: center;
        }
        
        .contact-item i {
            margin-left: 10px;
            color: var(--accent-color);
        }
        
        .section {
            padding: 25px 30px;
            border-bottom: 1px solid var(--border-color);
        }
        
        .section:last-child {
            border-bottom: none;
        }
        
        .section-title {
            color: var(--primary-color);
            font-size: 1.5rem;
            margin-bottom: 15px;
            padding-bottom: 5px;
            border-bottom: 2px solid var(--accent-color);
            display: inline-block;
        }
        
        .education-item, .skill-item {
            margin-bottom: 15px;
        }
        
        .education-title {
            font-weight: 700;
            font-size: 1.1rem;
            color: var(--secondary-color);
        }
        
        .education-details {
            margin-top: 5px;
        }
        
        .skills-container {
            display: flex;
            flex-wrap: wrap;
        }
        
        .skill-category {
            flex: 1;
            min-width: 250px;
            margin-bottom: 20px;
        }
        
        .skill-name {
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        .skill-bar {
            height: 10px;
            background-color: var(--light-color);
            border-radius: 5px;
            margin-bottom: 15px;
            overflow: hidden;
        }
        
        .skill-level {
            height: 100%;
            background-color: var(--accent-color);
            border-radius: 5px;
        }
        
        .languages-list {
            list-style-type: none;
        }
        
        .languages-list li {
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
        }
        
        .language-level {
            color: var(--accent-color);
            font-weight: 500;
        }
        
        /* تنسيق زر التحميل */
        .download-section {
            text-align: center;
            padding: 20px;
            background-color: var(--light-color);
        }
        
        .download-btn {
            background: linear-gradient(90deg, var(--accent-color), var(--secondary-color));
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: inherit;
            box-shadow: 0 4px 15px rgba(67, 97, 238, 0.3);
        }
        
        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(67, 97, 238, 0.4);
        }
        
        .download-btn:active {
            transform: translateY(0);
        }
        
        /* إضافة تنسيق لرسالة الحالة */
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            display: none;
            font-weight: 500;
        }
        
        .success {
            background: rgba(144, 238, 144, 0.2);
            border: 1px solid rgba(144, 238, 144, 0.5);
            color: #2e8b57;
        }
        
        @media (max-width: 768px) {
            .cv-container {
                margin: 10px;
                border-radius: 5px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 1.8rem;
            }
            
            .section {
                padding: 15px 20px;
            }
            
            .download-btn {
                padding: 10px 20px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <div class="cv-container" id="cv-container">
        <div class="header">
            <h1>علي خالد عويد خالد</h1>
            <h2>طالب هندسة</h2>
        </div>
        
        <div class="contact-info">
            <div class="contact-item">
                <i class="fas fa-map-marker-alt"></i>
                <span>بصرة/أبو الخصيب-محيلة، العراق</span>
            </div>
            <div class="contact-item">
                <i class="fas fa-phone"></i>
                <span>07730411335</span>
            </div>
            <div class="contact-item">
                <i class="fas fa-envelope"></i>
                <span><EMAIL></span>
            </div>
            <div class="contact-item">
                <i class="fas fa-calendar"></i>
                <span>14/11/2005</span>
            </div>
        </div>
        
        <div class="section">
            <h3 class="section-title">المؤهلات التعليمية</h3>
            <div class="education-item">
                <div class="education-title">شهادة الإعدادية</div>
                <div class="education-details">
                    <p>حاصل على شهادة الإعدادية</p>
                </div>
            </div>
            <div class="education-item">
                <div class="education-title">الدراسة الحالية</div>
                <div class="education-details">
                    <p>طالب في كلية الهندسة</p>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h3 class="section-title">المهارات</h3>
            <div class="skills-container">
                <div class="skill-category">
                    <div class="skill-item">
                        <div class="skill-name">تصميم مواقع</div>
                        <div class="skill-bar">
                            <div class="skill-level" style="width: 85%;"></div>
                        </div>
                    </div>
                    <div class="skill-item">
                        <div class="skill-name">Microsoft Excel</div>
                        <div class="skill-bar">
                            <div class="skill-level" style="width: 80%;"></div>
                        </div>
                    </div>
                    <div class="skill-item">
                        <div class="skill-name">Microsoft Word</div>
                        <div class="skill-bar">
                            <div class="skill-level" style="width: 90%;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h3 class="section-title">اللغات</h3>
            <ul class="languages-list">
                <li>
                    <span class="language-name">العربية</span>
                    <span class="language-level">اللغة الأم</span>
                </li>
                <li>
                    <span class="language-name">الإنجليزية</span>
                    <span class="language-level">متوسط</span>
                </li>
            </ul>
        </div>
    </div>
    
    <!-- قسم زر التحميل -->
    <div class="download-section">
        <button class="download-btn" onclick="downloadCV()">
            <i class="fas fa-download"></i> تحميل السيرة الذاتية
        </button>
        <div class="status" id="status"></div>
    </div>
    
    <!-- Font Awesome للأيقونات -->
    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
    
    <!-- سكريبت لتحميل السيرة الذاتية كصورة -->
    <script>
        async function downloadCV() {
            try {
                // إظهار رسالة التحميل
                const status = document.getElementById('status');
                status.textContent = 'جاري إنشاء الصورة...';
                status.className = 'status success';
                status.style.display = 'block';
                
                // إخفاء زر التحميل مؤقت<|im_start|>akah من الصورة
                const downloadSection = document.querySelector('.download-section');
                const originalDisplay = downloadSection.style.display;
                downloadSection.style.display = 'none';
                
                // انتظار لحظة للتأكد من تحديث DOM
                await new Promise(resolve => setTimeout(resolve, 100));
                
                // تحويل السيرة الذاتية إلى صورة
                const element = document.getElementById('cv-container');
                const canvas = await html2canvas(element, {
                    scale: 2,
                    backgroundColor: '#ffffff',
                    allowTaint: true,
                    useCORS: true,
                    logging: false
                });
                
                // تحويل الصورة إلى blob وتحميلها
                canvas.toBlob(function(blob) {
                    if (!blob) {
                        throw new Error('فشل في إنشاء الصورة');
                    }
                    
                    const url = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = 'السيرة_الذاتية_علي_خالد.png';
                    link.style.display = 'none';
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    
                    // تنظيف
                    setTimeout(() => {
                        URL.revokeObjectURL(url);
                    }, 1000);
                    
                    // إظهار زر التحميل مرة أخرى
                    downloadSection.style.display = originalDisplay;
                    
                    // تحديث رسالة النجاح
                    status.textContent = 'تم تحميل السيرة الذاتية بنجاح! ✅';
                    
                    // إخفاء رسالة النجاح بعد 5 ثوانٍ
                    setTimeout(() => {
                        status.style.display = 'none';
                    }, 5000);
                    
                }, 'image/png', 0.95);
                
            } catch (error) {
                console.error('خطأ في التحميل:', error);
                
                // إظهار زر التحميل مرة أخرى
                document.querySelector('.download-section').style.display = 'block';
                
                // إظهار رسالة الخطأ
                const status = document.getElementById('status');
                status.textContent = 'حدث خطأ في تحميل الصورة: ' + error.message;
                status.className = 'status error';
                status.style.display = 'block';
                
                // إخفاء رسالة الخطأ بعد 5 ثوانٍ
                setTimeout(() => {
                    status.style.display = 'none';
                }, 5000);
            }
        }
    </script>
</body>
</html>