<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مولد ردود إنستغرام</title>
    
    <!-- Arabic Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;600;700&family=Amiri:wght@400;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans Arabic', Arial, sans-serif;
            line-height: 1.6;
            color: #262626;
            background: #fafafa;
            direction: rtl;
            min-height: 100vh;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Header */
        .header {
            background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);
            color: white;
            text-align: center;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        /* Main Panel */
        .main-panel {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .panel-content {
            padding: 2rem;
        }

        /* Method Tabs */
        .method-tabs {
            display: flex;
            background: #f5f5f5;
            border-radius: 10px;
            padding: 5px;
            margin-bottom: 2rem;
        }

        .method-tab {
            flex: 1;
            background: transparent;
            border: none;
            padding: 1rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            color: #666;
        }

        .method-tab.active {
            background: linear-gradient(45deg, #e1306c, #c13584);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(225, 48, 108, 0.3);
        }

        .method-tab i {
            margin-left: 8px;
        }

        /* Section */
        .section {
            margin-bottom: 2rem;
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: #262626;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
        }

        .section-title i {
            margin-left: 10px;
            color: #e1306c;
        }

        /* Upload Area */
        .upload-area {
            border: 3px dashed #ddd;
            border-radius: 15px;
            padding: 3rem 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #fafafa;
        }

        .upload-area:hover {
            border-color: #e1306c;
            background: rgba(225, 48, 108, 0.05);
            transform: translateY(-2px);
        }

        .upload-area.dragover {
            border-color: #e1306c;
            background: rgba(225, 48, 108, 0.1);
        }

        .upload-icon {
            font-size: 4rem;
            color: #999;
            margin-bottom: 1rem;
        }

        .upload-text {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 0.5rem;
            font-weight: 600;
        }

        .upload-subtext {
            font-size: 1rem;
            color: #999;
            margin-bottom: 1rem;
        }

        .upload-button {
            background: linear-gradient(45deg, #e1306c, #c13584);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 10px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .upload-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(225, 48, 108, 0.3);
        }

        .file-input {
            display: none;
        }

        /* Image Preview */
        .image-preview {
            margin-top: 1rem;
            text-align: center;
        }

        .preview-image {
            max-width: 100%;
            max-height: 300px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        /* Form Elements */
        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #262626;
        }

        .form-input, .form-textarea, .form-select {
            width: 100%;
            padding: 1rem;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-family: inherit;
            font-size: 1rem;
            direction: rtl;
            transition: all 0.3s ease;
        }

        .form-input:focus, .form-textarea:focus, .form-select:focus {
            outline: none;
            border-color: #e1306c;
            box-shadow: 0 0 0 3px rgba(225, 48, 108, 0.1);
        }

        .form-textarea {
            min-height: 120px;
            resize: vertical;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        /* Font Controls */
        .font-controls {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 10px;
        }

        .font-control {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
        }

        .font-control label {
            font-size: 0.9rem;
            font-weight: 600;
            color: #666;
        }

        .font-size-slider {
            width: 120px;
        }

        .font-size-value {
            background: #e1306c;
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
        }

        /* Generate Button */
        .generate-button {
            background: linear-gradient(45deg, #e1306c, #c13584);
            color: white;
            border: none;
            padding: 1.5rem 3rem;
            border-radius: 15px;
            font-size: 1.3rem;
            font-weight: 700;
            cursor: pointer;
            width: 100%;
            margin: 2rem 0;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .generate-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(225, 48, 108, 0.4);
        }

        .generate-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .generate-button i {
            margin-left: 10px;
        }

        /* Result Section */
        .result-section {
            text-align: center;
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .result-canvas {
            max-width: 100%;
            border-radius: 15px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
            margin-bottom: 2rem;
        }

        .download-button {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 10px;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .download-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.3);
        }

        /* Status Messages */
        .status-message {
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
            text-align: center;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .status-message.success {
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
            border: 2px solid rgba(40, 167, 69, 0.3);
        }

        .status-message.error {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
            border: 2px solid rgba(220, 53, 69, 0.3);
        }

        .status-message.info {
            background: rgba(23, 162, 184, 0.1);
            color: #17a2b8;
            border: 2px solid rgba(23, 162, 184, 0.3);
        }

        .hidden {
            display: none;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .panel-content {
                padding: 1rem;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .font-controls {
                flex-direction: column;
                align-items: stretch;
            }
            
            .header h1 {
                font-size: 1.5rem;
            }
            
            .generate-button {
                padding: 1rem 2rem;
                font-size: 1.1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1><i class="fab fa-instagram"></i> مولد ردود إنستغرام</h1>
            <p>أنشئ صور ردود احترافية على تعليقات إنستغرام باللغة العربية</p>
        </div>

        <!-- Main Panel -->
        <div class="main-panel">
            <div class="panel-content">
                <!-- Method Selection -->
                <div class="section">
                    <h3 class="section-title">
                        <i class="fas fa-cog"></i>
                        اختر طريقة إدخال التعليق الأصلي
                    </h3>
                    <div class="method-tabs">
                        <button class="method-tab active" onclick="switchMethod('image')">
                            <i class="fas fa-image"></i>
                            رفع صورة
                        </button>
                        <button class="method-tab" onclick="switchMethod('text')">
                            <i class="fas fa-keyboard"></i>
                            كتابة نص
                        </button>
                    </div>
                </div>

                <!-- Image Upload Method -->
                <div class="section" id="imageMethod">
                    <h3 class="section-title">
                        <i class="fas fa-cloud-upload-alt"></i>
                        رفع صورة التعليق
                    </h3>
                    <div class="upload-area" onclick="document.getElementById('fileInput').click()">
                        <div class="upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <div class="upload-text">انقر لرفع صورة التعليق</div>
                        <div class="upload-subtext">يدعم JPG, PNG (حد أقصى 10MB)</div>
                        <button class="upload-button" type="button">اختيار صورة</button>
                    </div>
                    <input type="file" id="fileInput" class="file-input" accept="image/*">
                    <div class="image-preview hidden" id="imagePreview">
                        <img id="previewImage" class="preview-image" alt="معاينة الصورة">
                    </div>
                </div>

                <!-- Text Input Method -->
                <div class="section hidden" id="textMethod">
                    <h3 class="section-title">
                        <i class="fas fa-edit"></i>
                        كتابة تفاصيل التعليق
                    </h3>
                    <div class="form-group">
                        <label>اسم المستخدم:</label>
                        <input type="text" id="username" class="form-input" value="user123" placeholder="اسم المستخدم">
                    </div>
                    <div class="form-group">
                        <label>نص التعليق الأصلي:</label>
                        <textarea id="originalComment" class="form-textarea" placeholder="اكتب التعليق الأصلي هنا..."></textarea>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>الوقت:</label>
                            <input type="text" id="commentTime" class="form-input" value="منذ ساعة" placeholder="منذ ساعة">
                        </div>
                        <div class="form-group">
                            <label>عدد الإعجابات:</label>
                            <input type="number" id="commentLikes" class="form-input" value="5" min="0" placeholder="5">
                        </div>
                    </div>
                </div>

                <!-- Reply Section -->
                <div class="section">
                    <h3 class="section-title">
                        <i class="fas fa-reply"></i>
                        كتابة الرد
                    </h3>
                    <div class="form-group">
                        <label>نص الرد:</label>
                        <textarea id="replyText" class="form-textarea" placeholder="اكتب ردك هنا..."></textarea>
                    </div>
                    <div class="font-controls">
                        <div class="font-control">
                            <label>نوع الخط:</label>
                            <select id="fontSelect" class="form-select">
                                <option value="Noto Sans Arabic">Noto Sans Arabic</option>
                                <option value="Amiri">Amiri</option>
                            </select>
                        </div>
                        <div class="font-control">
                            <label>حجم الخط:</label>
                            <input type="range" id="fontSize" class="font-size-slider" min="16" max="36" value="24">
                        </div>
                        <div class="font-control">
                            <span class="font-size-value" id="fontSizeValue">24px</span>
                        </div>
                    </div>
                </div>

                <!-- Generate Button -->
                <button class="generate-button" onclick="generateImage()">
                    <i class="fas fa-magic"></i>
                    إنشاء صورة الرد
                </button>

                <!-- Status Messages -->
                <div id="statusMessage" class="status-message hidden"></div>
            </div>
        </div>

        <!-- Result Section -->
        <div class="result-section hidden" id="resultSection">
            <h3 class="section-title">
                <i class="fas fa-image"></i>
                الصورة النهائية
            </h3>
            <canvas id="resultCanvas" class="result-canvas"></canvas>
            <button class="download-button" onclick="downloadImage()">
                <i class="fas fa-download"></i>
                تحميل الصورة
            </button>
        </div>
    </div>

    <script>
        // Global variables
        let uploadedImage = null;
        let currentMethod = 'image';

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeEventListeners();
            updateFontSizeDisplay();
        });

        // Initialize event listeners
        function initializeEventListeners() {
            // File input
            document.getElementById('fileInput').addEventListener('change', handleFileSelect);

            // Font size slider
            document.getElementById('fontSize').addEventListener('input', updateFontSizeDisplay);

            // Drag and drop
            const uploadArea = document.querySelector('.upload-area');
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('dragleave', handleDragLeave);
            uploadArea.addEventListener('drop', handleDrop);

            // Prevent default drag behaviors
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                uploadArea.addEventListener(eventName, preventDefaults, false);
                document.body.addEventListener(eventName, preventDefaults, false);
            });
        }

        // Switch between input methods
        function switchMethod(method) {
            currentMethod = method;

            // Update tabs
            document.querySelectorAll('.method-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');

            // Show/hide sections
            document.getElementById('imageMethod').classList.toggle('hidden', method !== 'image');
            document.getElementById('textMethod').classList.toggle('hidden', method !== 'text');
        }

        // Update font size display
        function updateFontSizeDisplay() {
            const fontSize = document.getElementById('fontSize').value;
            document.getElementById('fontSizeValue').textContent = fontSize + 'px';
        }

        // Prevent default drag behaviors
        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        // Handle drag over
        function handleDragOver(e) {
            preventDefaults(e);
            e.currentTarget.classList.add('dragover');
        }

        // Handle drag leave
        function handleDragLeave(e) {
            preventDefaults(e);
            e.currentTarget.classList.remove('dragover');
        }

        // Handle drop
        function handleDrop(e) {
            preventDefaults(e);
            e.currentTarget.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        }

        // Handle file selection
        function handleFileSelect(e) {
            const file = e.target.files[0];
            if (file) {
                handleFile(file);
            }
        }

        // Handle file processing
        function handleFile(file) {
            // Validate file type
            if (!file.type.startsWith('image/')) {
                showStatus('يرجى اختيار ملف صورة صالح (JPG, PNG)', 'error');
                return;
            }

            // Validate file size (max 10MB)
            if (file.size > 10 * 1024 * 1024) {
                showStatus('حجم الملف كبير جداً. يرجى اختيار صورة أصغر من 10 ميجابايت', 'error');
                return;
            }

            // Read and display the image
            const reader = new FileReader();
            reader.onload = function(e) {
                const img = new Image();
                img.onload = function() {
                    uploadedImage = img;
                    displayImagePreview(e.target.result);
                    showStatus('تم رفع الصورة بنجاح!', 'success');
                };
                img.onerror = function() {
                    showStatus('فشل في تحميل الصورة. يرجى المحاولة مرة أخرى', 'error');
                };
                img.src = e.target.result;
            };
            reader.onerror = function() {
                showStatus('فشل في قراءة الملف. يرجى المحاولة مرة أخرى', 'error');
            };
            reader.readAsDataURL(file);
        }

        // Display image preview
        function displayImagePreview(imageSrc) {
            const previewContainer = document.getElementById('imagePreview');
            const previewImage = document.getElementById('previewImage');

            previewImage.src = imageSrc;
            previewContainer.classList.remove('hidden');
        }

        // Generate image
        function generateImage() {
            try {
                // Validate inputs
                if (currentMethod === 'image' && !uploadedImage) {
                    showStatus('يرجى رفع صورة التعليق أولاً', 'error');
                    return;
                }

                if (currentMethod === 'text') {
                    const originalComment = document.getElementById('originalComment').value.trim();
                    if (!originalComment) {
                        showStatus('يرجى كتابة نص التعليق الأصلي', 'error');
                        return;
                    }
                }

                const replyText = document.getElementById('replyText').value.trim();
                if (!replyText) {
                    showStatus('يرجى كتابة نص الرد', 'error');
                    return;
                }

                // Show loading state
                const button = document.querySelector('.generate-button');
                const originalText = button.innerHTML;
                button.disabled = true;
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإنشاء...';

                // Create image with delay for UI update
                setTimeout(() => {
                    try {
                        createCombinedImage();
                        showStatus('تم إنشاء الصورة بنجاح!', 'success');
                        document.getElementById('resultSection').classList.remove('hidden');

                        // Scroll to result
                        document.getElementById('resultSection').scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    } catch (error) {
                        console.error('Error creating image:', error);
                        showStatus('حدث خطأ في إنشاء الصورة: ' + error.message, 'error');
                    } finally {
                        button.disabled = false;
                        button.innerHTML = originalText;
                    }
                }, 500);
            } catch (error) {
                console.error('Error in generateImage:', error);
                showStatus('حدث خطأ غير متوقع', 'error');
            }
        }

        // Create combined image
        function createCombinedImage() {
            const canvas = document.getElementById('resultCanvas');
            const ctx = canvas.getContext('2d');

            // Set canvas size for Instagram format
            canvas.width = 1080;
            canvas.height = 1350;

            // Enable high-quality rendering
            ctx.imageSmoothingEnabled = true;
            ctx.imageSmoothingQuality = 'high';
            ctx.textBaseline = 'top';

            // Background
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            let yPosition = 40;

            // Draw original comment/image
            if (currentMethod === 'image' && uploadedImage) {
                yPosition = drawOriginalImage(ctx, yPosition);
            } else if (currentMethod === 'text') {
                yPosition = drawOriginalComment(ctx, yPosition);
            }

            // Add separator
            yPosition += 30;
            ctx.strokeStyle = '#efefef';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(40, yPosition);
            ctx.lineTo(canvas.width - 40, yPosition);
            ctx.stroke();
            yPosition += 30;

            // Draw reply
            drawReply(ctx, yPosition);
        }

        // Draw original image
        function drawOriginalImage(ctx, y) {
            const canvas = ctx.canvas;
            const maxWidth = canvas.width - 80;
            const maxHeight = 600;

            // Calculate scaled dimensions
            let imgWidth = uploadedImage.width;
            let imgHeight = uploadedImage.height;

            const scaleX = maxWidth / imgWidth;
            const scaleY = maxHeight / imgHeight;
            const scale = Math.min(scaleX, scaleY);

            imgWidth *= scale;
            imgHeight *= scale;

            // Center the image
            const x = (canvas.width - imgWidth) / 2;

            // Draw shadow
            ctx.save();
            ctx.shadowColor = 'rgba(0,0,0,0.15)';
            ctx.shadowBlur = 20;
            ctx.shadowOffsetY = 10;

            // Draw image
            ctx.drawImage(uploadedImage, x, y, imgWidth, imgHeight);
            ctx.restore();

            return y + imgHeight;
        }

        // Draw original comment
        function drawOriginalComment(ctx, y) {
            const canvas = ctx.canvas;
            const username = document.getElementById('username').value || 'user123';
            const comment = document.getElementById('originalComment').value;
            const time = document.getElementById('commentTime').value || 'منذ ساعة';
            const likes = document.getElementById('commentLikes').value || '5';

            const boxWidth = canvas.width - 80;
            const boxHeight = 250;
            const x = 40;

            // Draw comment box with Instagram style
            ctx.save();
            ctx.shadowColor = 'rgba(0,0,0,0.1)';
            ctx.shadowBlur = 15;
            ctx.shadowOffsetY = 5;

            // Background
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(x, y, boxWidth, boxHeight);

            // Border
            ctx.strokeStyle = '#efefef';
            ctx.lineWidth = 1;
            ctx.strokeRect(x, y, boxWidth, boxHeight);
            ctx.restore();

            // Avatar
            const avatarSize = 40;
            const avatarX = x + 20;
            const avatarY = y + 20;

            // Avatar background (Instagram gradient)
            const gradient = ctx.createLinearGradient(avatarX, avatarY, avatarX + avatarSize, avatarY + avatarSize);
            gradient.addColorStop(0, '#f09433');
            gradient.addColorStop(0.25, '#e6683c');
            gradient.addColorStop(0.5, '#dc2743');
            gradient.addColorStop(0.75, '#cc2366');
            gradient.addColorStop(1, '#bc1888');

            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(avatarX + avatarSize/2, avatarY + avatarSize/2, avatarSize/2, 0, 2 * Math.PI);
            ctx.fill();

            // Avatar icon
            ctx.fillStyle = '#ffffff';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('👤', avatarX + avatarSize/2, avatarY + avatarSize/2 + 7);

            // Username
            ctx.fillStyle = '#262626';
            ctx.font = 'bold 22px "Noto Sans Arabic", Arial, sans-serif';
            ctx.textAlign = 'right';
            ctx.fillText(username, canvas.width - 60, y + 35);

            // Comment text
            ctx.fillStyle = '#262626';
            ctx.font = '20px "Noto Sans Arabic", Arial, sans-serif';
            ctx.textAlign = 'right';
            drawWrappedText(ctx, comment, canvas.width - 60, y + 80, boxWidth - 120, 28);

            // Time and likes
            ctx.fillStyle = '#8e8e8e';
            ctx.font = '16px "Noto Sans Arabic", Arial, sans-serif';
            ctx.textAlign = 'right';
            ctx.fillText(`${time} • ${likes} إعجاب`, canvas.width - 60, y + 200);

            return y + boxHeight;
        }

        // Draw reply
        function drawReply(ctx, y) {
            const canvas = ctx.canvas;
            const replyText = document.getElementById('replyText').value;
            const fontSize = document.getElementById('fontSize').value;
            const fontFamily = document.getElementById('fontSelect').value;

            const boxWidth = canvas.width - 80;
            const boxHeight = 200;
            const x = 40;

            // Draw reply box with Instagram style
            ctx.save();
            ctx.shadowColor = 'rgba(225, 48, 108, 0.3)';
            ctx.shadowBlur = 20;
            ctx.shadowOffsetY = 10;

            // Background gradient
            const gradient = ctx.createLinearGradient(x, y, x + boxWidth, y + boxHeight);
            gradient.addColorStop(0, '#e1306c');
            gradient.addColorStop(1, '#c13584');

            ctx.fillStyle = gradient;
            ctx.fillRect(x, y, boxWidth, boxHeight);
            ctx.restore();

            // Reply icon
            ctx.fillStyle = '#ffffff';
            ctx.font = '30px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('💬', x + 50, y + 40);

            // Reply label
            ctx.fillStyle = '#ffffff';
            ctx.font = 'bold 20px "Noto Sans Arabic", Arial, sans-serif';
            ctx.textAlign = 'right';
            ctx.fillText('ردك:', canvas.width - 60, y + 35);

            // Reply text
            ctx.fillStyle = '#ffffff';
            ctx.font = `bold ${fontSize}px "${fontFamily}", Arial, sans-serif`;
            ctx.textAlign = 'center';

            // Center the text vertically
            const textY = y + boxHeight / 2 - parseInt(fontSize) / 2;
            drawWrappedText(ctx, replyText, canvas.width / 2, textY, boxWidth - 120, parseInt(fontSize) + 8);
        }

        // Helper function to draw wrapped text
        function drawWrappedText(ctx, text, x, y, maxWidth, lineHeight) {
            if (!text || text.trim() === '') return;

            const words = text.split(' ');
            let line = '';
            let currentY = y;
            const lines = [];

            // Build lines array
            for (let i = 0; i < words.length; i++) {
                const testLine = line + words[i] + ' ';
                const metrics = ctx.measureText(testLine);
                const testWidth = metrics.width;

                if (testWidth > maxWidth && i > 0) {
                    lines.push(line.trim());
                    line = words[i] + ' ';
                } else {
                    line = testLine;
                }
            }
            if (line.trim() !== '') {
                lines.push(line.trim());
            }

            // Draw lines
            for (let i = 0; i < lines.length; i++) {
                ctx.fillText(lines[i], x, currentY);
                currentY += lineHeight;
            }
        }

        // Download image
        function downloadImage() {
            try {
                const canvas = document.getElementById('resultCanvas');
                if (!canvas) {
                    showStatus('لا توجد صورة للتحميل', 'error');
                    return;
                }

                canvas.toBlob(function(blob) {
                    if (!blob) {
                        showStatus('فشل في إنشاء الصورة للتحميل', 'error');
                        return;
                    }

                    const url = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = `رد_انستغرام_${new Date().getTime()}.png`;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    URL.revokeObjectURL(url);

                    showStatus('تم تحميل الصورة بنجاح!', 'success');
                }, 'image/png', 0.95);
            } catch (error) {
                console.error('Download error:', error);
                showStatus('حدث خطأ في تحميل الصورة', 'error');
            }
        }

        // Show status message
        function showStatus(message, type) {
            const statusEl = document.getElementById('statusMessage');
            statusEl.textContent = message;
            statusEl.className = `status-message ${type}`;
            statusEl.classList.remove('hidden');

            setTimeout(() => {
                statusEl.classList.add('hidden');
            }, 4000);
        }
    </script>
</body>
</html>
