<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مولد صور الردود على إنستغرام - AliToucan</title>
    <meta name="description" content="أداة احترافية لإنشاء صور الردود على تعليقات إنستغرام باللغة العربية">
    <meta name="keywords" content="إنستغرام, ردود, تعليقات, صور, عربي, AliToucan">
    
    <!-- Arabic Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Noto+Sans+Arabic:wght@300;400;600;700&family=Scheherazade+New:wght@400;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- html2canvas for image generation -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

    <!-- Additional libraries for enhanced functionality -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/intro.js/7.2.0/intro.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/intro.js/7.2.0/introjs.min.css">

    <!-- PWA Manifest -->
    <link rel="manifest" href="data:application/json;base64,eyJuYW1lIjoi2YXZiNmE2K8g2LXZiNixINin2YTYsdiv2YjYryDYudmE2Ykg2KXZhtiz2KrYutix2KfZhSAtIEFsaVRvdWNhbiIsInNob3J0X25hbWUiOiJBbGlUb3VjYW4gSW5zdGFncmFtIiwic3RhcnRfdXJsIjoiLiIsImRpc3BsYXkiOiJzdGFuZGFsb25lIiwiYmFja2dyb3VuZF9jb2xvciI6IiNmYWZhZmEiLCJ0aGVtZV9jb2xvciI6IiNlMTMwNmMiLCJpY29ucyI6W3sic3JjIjoiZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlCNGJXeHVjejBpYUhSMGNEb3ZMM2QzZHk1M00zUnZjbWN2TWpBd01DOXpkbWNpSUhacFpYZENiM2c5SWpBZ01DQXhNakFnTVRJd0lpQjNhV1IwYUQwaU1USXdJaUJvWldsbmFIUTlJakV5TUNJKyIsInNpemVzIjoiMTIweDEyMCIsInR5cGUiOiJpbWFnZS9zdmcreG1sIn1dfQ==">
    <meta name="theme-color" content="#e1306c">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">

    <style>
        :root {
            /* Enhanced Instagram-inspired color palette */
            --instagram-gradient: linear-gradient(135deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
            --instagram-gradient-hover: linear-gradient(135deg, #f5a623 0%, #ea7c4c 25%, #e13753 50%, #d12976 75%, #c71898 100%);
            --primary-color: #e1306c;
            --primary-hover: #c42d5f;
            --secondary-color: #833ab4;
            --secondary-hover: #7332a1;
            --accent-color: #fd1d1d;
            --success-color: #28a745;
            --success-hover: #218838;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;

            /* Enhanced neutral colors */
            --white: #ffffff;
            --light-gray: #fafafa;
            --lighter-gray: #f8f9fa;
            --medium-gray: #8e8e8e;
            --dark-gray: #262626;
            --darker-gray: #1a1a1a;
            --black: #000000;

            /* Enhanced theme variables */
            --bg-primary: #ffffff;
            --bg-secondary: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
            --bg-tertiary: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
            --text-primary: #262626;
            --text-secondary: #8e8e8e;
            --text-muted: #999999;
            --border-color: #dbdbdb;
            --border-hover: #c7c7c7;
            --shadow-color: rgba(0,0,0,0.1);

            /* Enhanced shadows with depth */
            --shadow-light: 0 2px 10px rgba(0,0,0,0.08);
            --shadow-medium: 0 4px 20px rgba(0,0,0,0.12);
            --shadow-heavy: 0 8px 30px rgba(0,0,0,0.16);
            --shadow-xl: 0 12px 40px rgba(0,0,0,0.2);
            --shadow-inset: inset 0 2px 4px rgba(0,0,0,0.06);
            --shadow-glow: 0 0 20px rgba(225, 48, 108, 0.3);

            /* Enhanced transitions and animations */
            --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-medium: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-bounce: 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            --transition-elastic: 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);

            /* Enhanced border radius */
            --radius-small: 8px;
            --radius-medium: 12px;
            --radius-large: 20px;
            --radius-xl: 24px;
            --radius-round: 50%;

            /* Typography scale */
            --font-size-xs: 0.75rem;
            --font-size-sm: 0.875rem;
            --font-size-base: 1rem;
            --font-size-lg: 1.125rem;
            --font-size-xl: 1.25rem;
            --font-size-2xl: 1.5rem;
            --font-size-3xl: 1.875rem;
            --font-size-4xl: 2.25rem;

            /* Spacing scale */
            --space-1: 0.25rem;
            --space-2: 0.5rem;
            --space-3: 0.75rem;
            --space-4: 1rem;
            --space-5: 1.25rem;
            --space-6: 1.5rem;
            --space-8: 2rem;
            --space-10: 2.5rem;
            --space-12: 3rem;
            --space-16: 4rem;

            /* Arabic calligraphy gradients */
            --arabic-gold: linear-gradient(135deg, #d4af37 0%, #ffd700 50%, #b8941f 100%);
            --arabic-emerald: linear-gradient(135deg, #50c878 0%, #00ff7f 50%, #228b22 100%);
            --arabic-sapphire: linear-gradient(135deg, #0f52ba 0%, #4169e1 50%, #000080 100%);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        *::before,
        *::after {
            box-sizing: border-box;
        }

        html {
            scroll-behavior: smooth;
            font-size: 16px;
        }

        body {
            font-family: 'Noto Sans Arabic', 'Amiri', Arial, sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background: var(--bg-secondary);
            direction: rtl;
            overflow-x: hidden;
            position: relative;
            min-height: 100vh;
        }

        /* Enhanced background with animated particles */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(225, 48, 108, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(131, 58, 180, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(240, 148, 51, 0.05) 0%, transparent 50%);
            z-index: -1;
            animation: backgroundShift 20s ease-in-out infinite;
        }

        @keyframes backgroundShift {
            0%, 100% {
                background:
                    radial-gradient(circle at 20% 80%, rgba(225, 48, 108, 0.1) 0%, transparent 50%),
                    radial-gradient(circle at 80% 20%, rgba(131, 58, 180, 0.1) 0%, transparent 50%),
                    radial-gradient(circle at 40% 40%, rgba(240, 148, 51, 0.05) 0%, transparent 50%);
            }
            50% {
                background:
                    radial-gradient(circle at 80% 20%, rgba(225, 48, 108, 0.1) 0%, transparent 50%),
                    radial-gradient(circle at 20% 80%, rgba(131, 58, 180, 0.1) 0%, transparent 50%),
                    radial-gradient(circle at 60% 60%, rgba(240, 148, 51, 0.05) 0%, transparent 50%);
            }
        }

        /* Enhanced scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--lighter-gray);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--instagram-gradient);
            border-radius: var(--radius-small);
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--instagram-gradient-hover);
        }

        /* Enhanced selection */
        ::selection {
            background: rgba(225, 48, 108, 0.2);
            color: var(--text-primary);
        }

        /* Focus styles for accessibility */
        *:focus {
            outline: 2px solid var(--primary-color);
            outline-offset: 2px;
        }

        /* Enhanced animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes fadeInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes scaleIn {
            from {
                opacity: 0;
                transform: scale(0.9);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        @keyframes shimmer {
            0% {
                background-position: -200% 0;
            }
            100% {
                background-position: 200% 0;
            }
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-10px);
            }
        }

        @keyframes glow {
            0%, 100% {
                box-shadow: 0 0 20px rgba(225, 48, 108, 0.3);
            }
            50% {
                box-shadow: 0 0 30px rgba(225, 48, 108, 0.5);
            }
        }

        /* Animation classes */
        .animate-fade-in-up {
            animation: fadeInUp 0.6s var(--transition-medium) forwards;
        }

        .animate-fade-in-down {
            animation: fadeInDown 0.6s var(--transition-medium) forwards;
        }

        .animate-fade-in-left {
            animation: fadeInLeft 0.6s var(--transition-medium) forwards;
        }

        .animate-fade-in-right {
            animation: fadeInRight 0.6s var(--transition-medium) forwards;
        }

        .animate-scale-in {
            animation: scaleIn 0.4s var(--transition-bounce) forwards;
        }

        .animate-pulse {
            animation: pulse 2s infinite;
        }

        .animate-float {
            animation: float 3s ease-in-out infinite;
        }

        .animate-glow {
            animation: glow 2s ease-in-out infinite;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        /* Enhanced Header */
        .header {
            background: var(--bg-tertiary);
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-medium);
            padding: var(--space-4) 0;
            margin-bottom: var(--space-8);
            position: sticky;
            top: 0;
            z-index: 100;
            border-bottom: 1px solid rgba(225, 48, 108, 0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: var(--space-4);
        }

        .logo {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            font-size: var(--font-size-xl);
            font-weight: 700;
            color: var(--primary-color);
            text-decoration: none;
            transition: var(--transition-medium);
            position: relative;
        }

        .logo:hover {
            transform: scale(1.05);
        }

        .logo i {
            font-size: var(--font-size-3xl);
            background: var(--instagram-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: float 3s ease-in-out infinite;
        }

        .brand-text {
            background: var(--instagram-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-family: 'Amiri', serif;
            position: relative;
        }

        .brand-text::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--instagram-gradient);
            transition: width var(--transition-medium);
        }

        .logo:hover .brand-text::after {
            width: 100%;
        }

        .header-nav {
            display: flex;
            align-items: center;
            gap: var(--space-4);
        }

        .nav-item {
            color: var(--text-secondary);
            font-weight: 500;
            font-size: var(--font-size-sm);
            padding: var(--space-2) var(--space-3);
            border-radius: var(--radius-medium);
            transition: var(--transition-fast);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .nav-item:hover {
            color: var(--primary-color);
            background: rgba(225, 48, 108, 0.1);
            transform: translateY(-2px);
        }

        .tutorial-btn {
            background: var(--instagram-gradient);
            color: var(--white);
            border: none;
            padding: var(--space-2) var(--space-4);
            border-radius: var(--radius-medium);
            font-size: var(--font-size-sm);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-medium);
            box-shadow: var(--shadow-light);
            position: relative;
            overflow: hidden;
        }

        .tutorial-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left var(--transition-medium);
        }

        .tutorial-btn:hover::before {
            left: 100%;
        }

        .tutorial-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        /* Enhanced Hero Section */
        .hero {
            background: var(--instagram-gradient);
            color: var(--white);
            padding: var(--space-16) 0;
            text-align: center;
            margin-bottom: var(--space-12);
            border-radius: var(--radius-xl);
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 30% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(255,255,255,0.05) 0%, transparent 50%);
            animation: backgroundShift 15s ease-in-out infinite;
        }

        .hero-content {
            position: relative;
            z-index: 1;
        }

        .hero h1 {
            font-size: var(--font-size-4xl);
            font-weight: 700;
            margin-bottom: var(--space-4);
            font-family: 'Amiri', serif;
            text-shadow: 0 2px 10px rgba(0,0,0,0.2);
            line-height: 1.2;
        }

        .hero p {
            font-size: var(--font-size-lg);
            opacity: 0.95;
            max-width: 600px;
            margin: 0 auto var(--space-6);
            line-height: 1.6;
            text-shadow: 0 1px 5px rgba(0,0,0,0.1);
        }

        .hero-features {
            display: flex;
            justify-content: center;
            gap: var(--space-8);
            margin-top: var(--space-8);
            flex-wrap: wrap;
        }

        .hero-feature {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            font-size: var(--font-size-sm);
            opacity: 0.9;
        }

        .hero-feature i {
            font-size: var(--font-size-lg);
            color: rgba(255,255,255,0.8);
        }
        
        /* Enhanced Main Panel */
        .main-panel {
            background: var(--white);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-xl);
            overflow: hidden;
            margin-bottom: var(--space-8);
            border: 1px solid rgba(225, 48, 108, 0.1);
            position: relative;
        }

        .main-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--instagram-gradient);
        }

        .panel-header {
            background: var(--instagram-gradient);
            color: var(--white);
            padding: var(--space-6);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .panel-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 50%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 50%, rgba(255,255,255,0.05) 0%, transparent 50%);
        }

        .panel-header h2 {
            font-size: var(--font-size-2xl);
            font-weight: 700;
            font-family: 'Amiri', serif;
            position: relative;
            z-index: 1;
            text-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }

        .panel-content {
            padding: var(--space-8);
            background: var(--bg-tertiary);
        }

        /* Enhanced Upload Section */
        .upload-section {
            margin-bottom: var(--space-8);
        }

        .section-title {
            font-size: var(--font-size-xl);
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--space-4);
            display: flex;
            align-items: center;
            gap: var(--space-2);
            position: relative;
        }

        .section-title::after {
            content: '';
            flex: 1;
            height: 2px;
            background: linear-gradient(to left, transparent, var(--border-color), transparent);
            margin-right: var(--space-4);
        }

        .section-title i {
            color: var(--primary-color);
            font-size: var(--font-size-lg);
        }

        .upload-area {
            border: 3px dashed var(--border-color);
            border-radius: var(--radius-large);
            padding: var(--space-12) var(--space-8);
            text-align: center;
            transition: var(--transition-medium);
            cursor: pointer;
            background: var(--lighter-gray);
            position: relative;
            overflow: hidden;
        }

        .upload-area::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(225, 48, 108, 0.1), transparent);
            transition: left var(--transition-slow);
        }

        .upload-area:hover::before {
            left: 100%;
        }

        .upload-area.dragover {
            border-color: var(--primary-color);
            background: rgba(225, 48, 108, 0.05);
            transform: scale(1.02);
            box-shadow: var(--shadow-glow);
        }

        .upload-area.has-file {
            border-color: var(--success-color);
            background: rgba(40, 167, 69, 0.05);
            border-style: solid;
        }

        .upload-icon {
            font-size: var(--font-size-4xl);
            color: var(--medium-gray);
            margin-bottom: var(--space-4);
            transition: var(--transition-medium);
            position: relative;
            z-index: 1;
        }

        .upload-area.dragover .upload-icon,
        .upload-area.has-file .upload-icon {
            color: var(--primary-color);
            transform: scale(1.2);
            animation: pulse 1s ease-in-out;
        }

        .upload-text {
            font-size: var(--font-size-lg);
            color: var(--text-secondary);
            margin-bottom: var(--space-4);
            position: relative;
            z-index: 1;
            line-height: 1.5;
        }

        .upload-subtext {
            font-size: var(--font-size-sm);
            color: var(--text-muted);
            margin-bottom: var(--space-6);
            position: relative;
            z-index: 1;
        }

        .file-input {
            display: none;
        }

        .upload-button {
            background: var(--instagram-gradient);
            color: var(--white);
            border: none;
            padding: var(--space-3) var(--space-6);
            border-radius: var(--radius-medium);
            cursor: pointer;
            font-size: var(--font-size-base);
            font-weight: 600;
            transition: var(--transition-medium);
            box-shadow: var(--shadow-medium);
            position: relative;
            z-index: 1;
            overflow: hidden;
        }

        .upload-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: var(--instagram-gradient-hover);
            transition: left var(--transition-medium);
        }

        .upload-button:hover::before {
            left: 0;
        }

        .upload-button:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-heavy);
        }

        .upload-button span {
            position: relative;
            z-index: 1;
        }

        /* Image Preview Enhancement */
        .image-preview {
            margin-top: var(--space-6);
            padding: var(--space-6);
            background: var(--white);
            border-radius: var(--radius-large);
            box-shadow: var(--shadow-light);
            border: 1px solid var(--border-color);
        }

        .preview-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--space-4);
        }

        .preview-title {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            color: var(--text-primary);
            font-weight: 600;
        }

        .preview-actions {
            display: flex;
            gap: var(--space-2);
        }

        .preview-btn {
            background: var(--lighter-gray);
            border: 1px solid var(--border-color);
            color: var(--text-secondary);
            padding: var(--space-1) var(--space-2);
            border-radius: var(--radius-small);
            cursor: pointer;
            font-size: var(--font-size-xs);
            transition: var(--transition-fast);
        }

        .preview-btn:hover {
            background: var(--primary-color);
            color: var(--white);
            border-color: var(--primary-color);
        }

        .preview-container {
            text-align: center;
            background: var(--lighter-gray);
            padding: var(--space-4);
            border-radius: var(--radius-medium);
            position: relative;
            overflow: hidden;
        }

        .preview-image {
            max-width: 100%;
            max-height: 300px;
            border-radius: var(--radius-small);
            box-shadow: var(--shadow-medium);
            transition: var(--transition-medium);
        }

        .preview-image:hover {
            transform: scale(1.02);
        }

        /* Enhanced Reply Text Section */
        .reply-section {
            margin-bottom: var(--space-8);
        }

        /* Preset Templates */
        .preset-templates {
            background: var(--white);
            border-radius: var(--radius-large);
            padding: var(--space-6);
            margin-bottom: var(--space-6);
            border: 1px solid var(--border-color);
            box-shadow: var(--shadow-light);
        }

        .preset-title {
            font-size: var(--font-size-lg);
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--space-4);
            display: flex;
            align-items: center;
            gap: var(--space-2);
        }

        .preset-title i {
            color: var(--primary-color);
        }

        .preset-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-3);
        }

        .preset-btn {
            background: var(--lighter-gray);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-medium);
            padding: var(--space-3);
            cursor: pointer;
            transition: var(--transition-medium);
            display: flex;
            align-items: center;
            gap: var(--space-2);
            font-size: var(--font-size-sm);
            color: var(--text-primary);
            text-align: right;
        }

        .preset-btn:hover {
            background: var(--primary-color);
            color: var(--white);
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .preset-btn i {
            font-size: var(--font-size-base);
            opacity: 0.8;
        }

        .text-input-container {
            background: var(--white);
            padding: var(--space-6);
            border-radius: var(--radius-large);
            border: 1px solid var(--border-color);
            box-shadow: var(--shadow-light);
        }

        .textarea-wrapper {
            position: relative;
            margin-bottom: var(--space-4);
        }

        .reply-textarea {
            width: 100%;
            min-height: 140px;
            padding: var(--space-4);
            border: 2px solid var(--border-color);
            border-radius: var(--radius-medium);
            font-family: inherit;
            font-size: var(--font-size-lg);
            line-height: 1.6;
            resize: vertical;
            direction: rtl;
            text-align: right;
            background: var(--lighter-gray);
            transition: var(--transition-medium);
        }

        .reply-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: var(--shadow-glow);
            background: var(--white);
        }

        .textarea-tools {
            position: absolute;
            top: var(--space-2);
            left: var(--space-2);
            display: flex;
            gap: var(--space-1);
            opacity: 0;
            transition: var(--transition-fast);
        }

        .textarea-wrapper:hover .textarea-tools {
            opacity: 1;
        }

        .text-tool {
            background: var(--white);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-small);
            padding: var(--space-1);
            cursor: pointer;
            transition: var(--transition-fast);
            color: var(--text-secondary);
            font-size: var(--font-size-xs);
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .text-tool:hover {
            background: var(--primary-color);
            color: var(--white);
            border-color: var(--primary-color);
        }

        .text-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-4);
            gap: var(--space-4);
            flex-wrap: wrap;
            padding: var(--space-3);
            background: var(--lighter-gray);
            border-radius: var(--radius-medium);
        }

        .font-options, .size-options {
            display: flex;
            align-items: center;
            gap: var(--space-2);
        }

        .font-select, .background-select {
            padding: var(--space-2);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-small);
            font-family: inherit;
            background: var(--white);
            transition: var(--transition-fast);
        }

        .font-select:focus, .background-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(225, 48, 108, 0.1);
        }

        .font-size-slider {
            width: 120px;
            height: 6px;
            border-radius: 3px;
            background: var(--border-color);
            outline: none;
            transition: var(--transition-fast);
        }

        .font-size-slider::-webkit-slider-thumb {
            appearance: none;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: var(--instagram-gradient);
            cursor: pointer;
            box-shadow: var(--shadow-light);
        }

        #fontSizeValue {
            font-weight: 600;
            color: var(--primary-color);
            min-width: 45px;
            font-size: var(--font-size-sm);
        }

        /* Advanced Options */
        .advanced-options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--space-4);
            margin-bottom: var(--space-4);
            padding: var(--space-4);
            background: var(--lighter-gray);
            border-radius: var(--radius-medium);
        }

        .style-options, .background-options {
            display: flex;
            flex-direction: column;
            gap: var(--space-2);
        }

        .style-controls {
            display: flex;
            flex-direction: column;
            gap: var(--space-2);
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            cursor: pointer;
            font-size: var(--font-size-sm);
            position: relative;
        }

        .checkbox-label input[type="checkbox"] {
            display: none;
        }

        .checkmark {
            width: 18px;
            height: 18px;
            border: 2px solid var(--border-color);
            border-radius: var(--radius-small);
            position: relative;
            transition: var(--transition-fast);
        }

        .checkbox-label input[type="checkbox"]:checked + .checkmark {
            background: var(--instagram-gradient);
            border-color: var(--primary-color);
        }

        .checkbox-label input[type="checkbox"]:checked + .checkmark::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: var(--white);
            font-size: var(--font-size-xs);
            font-weight: bold;
        }

        .color-picker {
            width: 40px;
            height: 30px;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-small);
            cursor: pointer;
            transition: var(--transition-fast);
        }

        .color-picker:hover {
            border-color: var(--primary-color);
        }

        /* Preset Actions */
        .preset-actions {
            display: flex;
            gap: var(--space-3);
            justify-content: center;
        }

        .preset-action-btn {
            background: var(--lighter-gray);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-medium);
            padding: var(--space-2) var(--space-4);
            cursor: pointer;
            transition: var(--transition-medium);
            display: flex;
            align-items: center;
            gap: var(--space-2);
            font-size: var(--font-size-sm);
            color: var(--text-primary);
        }

        .preset-action-btn:hover {
            background: var(--secondary-color);
            color: var(--white);
            border-color: var(--secondary-color);
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        /* Generate Section */
        .generate-section {
            margin-bottom: 2rem;
        }

        .generate-controls {
            text-align: center;
        }

        .generate-button {
            background: var(--instagram-gradient);
            color: var(--white);
            border: none;
            padding: 1rem 3rem;
            border-radius: var(--radius-medium);
            font-size: 1.2rem;
            font-weight: 700;
            cursor: pointer;
            transition: var(--transition-medium);
            box-shadow: var(--shadow-medium);
            margin-bottom: 1rem;
        }

        .generate-button:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-heavy);
        }

        .generate-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .format-options {
            display: flex;
            justify-content: center;
            gap: 2rem;
            flex-wrap: wrap;
        }

        .format-options label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
            padding: 0.5rem 1rem;
            border-radius: var(--radius-small);
            transition: var(--transition-fast);
        }

        .format-options label:hover {
            background: var(--light-gray);
        }

        /* Result Section */
        .result-section {
            margin-bottom: 2rem;
        }

        .result-container {
            text-align: center;
        }

        .result-preview {
            margin-bottom: 2rem;
            background: var(--light-gray);
            padding: 2rem;
            border-radius: var(--radius-medium);
        }

        .download-controls {
            display: flex;
            justify-content: center;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .download-button, .share-button {
            padding: 0.8rem 2rem;
            border: none;
            border-radius: var(--radius-medium);
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-fast);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .download-button {
            background: var(--success-color);
            color: var(--white);
        }

        .share-button {
            background: var(--secondary-color);
            color: var(--white);
        }

        .download-button:hover, .share-button:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        /* Status Messages */
        .status-container {
            margin-top: 2rem;
        }

        .status-message {
            padding: 1rem;
            border-radius: var(--radius-small);
            text-align: center;
            font-weight: 500;
            margin-bottom: 1rem;
        }

        .status-message.success {
            background: rgba(40, 167, 69, 0.1);
            color: var(--success-color);
            border: 1px solid rgba(40, 167, 69, 0.3);
        }

        .status-message.error {
            background: rgba(220, 53, 69, 0.1);
            color: var(--danger-color);
            border: 1px solid rgba(220, 53, 69, 0.3);
        }

        .status-message.info {
            background: rgba(23, 162, 184, 0.1);
            color: var(--info-color);
            border: 1px solid rgba(23, 162, 184, 0.3);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                padding: 0 15px;
            }

            .hero h1 {
                font-size: 2rem;
            }

            .hero p {
                font-size: 1rem;
            }

            .panel-content {
                padding: 1.5rem;
            }

            .upload-area {
                padding: 2rem 1rem;
            }

            .text-options {
                flex-direction: column;
                align-items: stretch;
            }

            .format-options {
                flex-direction: column;
                gap: 1rem;
            }

            .download-controls {
                flex-direction: column;
            }

            .generate-button {
                padding: 0.8rem 2rem;
                font-size: 1rem;
            }
        }

        /* Loading Animation */
        .loading {
            position: relative;
            overflow: hidden;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { left: -100%; }
            100% { left: 100%; }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header animate-fade-in-down">
        <div class="container">
            <div class="header-content">
                <a href="#" class="logo">
                    <i class="fab fa-instagram"></i>
                    <span class="brand-text">AliToucan</span>
                </a>
                <nav class="header-nav">
                    <div class="nav-item" onclick="showPresets()">
                        <i class="fas fa-templates"></i>
                        قوالب جاهزة
                    </div>
                    <div class="nav-item" onclick="showKeyboardShortcuts()">
                        <i class="fas fa-keyboard"></i>
                        اختصارات
                    </div>
                    <button class="tutorial-btn" onclick="startTutorial()" data-intro="ابدأ الجولة التعريفية لتعلم كيفية استخدام الموقع" data-step="1">
                        <i class="fas fa-question-circle"></i>
                        جولة تعريفية
                    </button>
                </nav>
            </div>
        </div>
    </header>

    <div class="container">
        <!-- Hero Section -->
        <section class="hero animate-fade-in-up">
            <div class="hero-content">
                <h1>مولد صور الردود على إنستغرام</h1>
                <p>أنشئ صور ردود احترافية على تعليقات إنستغرام باللغة العربية بسهولة وجودة عالية مع دعم كامل للخطوط العربية والتصميم الاحترافي</p>
                <div class="hero-features">
                    <div class="hero-feature">
                        <i class="fas fa-magic"></i>
                        <span>تصميم احترافي</span>
                    </div>
                    <div class="hero-feature">
                        <i class="fas fa-font"></i>
                        <span>خطوط عربية عالية الجودة</span>
                    </div>
                    <div class="hero-feature">
                        <i class="fas fa-mobile-alt"></i>
                        <span>متوافق مع الجوال</span>
                    </div>
                    <div class="hero-feature">
                        <i class="fas fa-download"></i>
                        <span>تحميل فوري</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Main Panel -->
        <div class="main-panel">
            <div class="panel-header">
                <h2>إنشاء صورة الرد</h2>
            </div>
            <div class="panel-content">
                <!-- Upload Section -->
                <div class="upload-section animate-fade-in-left" data-intro="ارفع صورة التعليق الذي تريد الرد عليه هنا" data-step="2">
                    <h3 class="section-title">
                        <i class="fas fa-upload"></i>
                        رفع صورة التعليق
                    </h3>
                    <div class="upload-area" id="uploadArea">
                        <div class="upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <div class="upload-text">
                            اسحب وأفلت صورة التعليق هنا أو انقر للاختيار
                        </div>
                        <div class="upload-subtext">
                            الحد الأقصى: 10 ميجابايت | الصيغ المدعومة: JPG, PNG, WebP
                        </div>
                        <button class="upload-button" onclick="document.getElementById('fileInput').click()">
                            <span>
                                <i class="fas fa-folder-open"></i>
                                اختيار صورة
                            </span>
                        </button>
                        <input type="file" id="fileInput" class="file-input" accept="image/*,image/webp">
                    </div>

                    <!-- Enhanced Image Preview -->
                    <div class="image-preview animate-scale-in" id="imagePreview" style="display: none;">
                        <div class="preview-header">
                            <div class="preview-title">
                                <i class="fas fa-eye"></i>
                                معاينة الصورة المرفوعة
                            </div>
                            <div class="preview-actions">
                                <button class="preview-btn" onclick="cropImage()" title="قص الصورة">
                                    <i class="fas fa-crop"></i>
                                </button>
                                <button class="preview-btn" onclick="rotateImage()" title="تدوير الصورة">
                                    <i class="fas fa-redo"></i>
                                </button>
                                <button class="preview-btn" onclick="removeImage()" title="إزالة الصورة">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        <div class="preview-container">
                            <img id="previewImage" class="preview-image">
                        </div>
                    </div>
                </div>

                <!-- Reply Text Section -->
                <div class="reply-section animate-fade-in-right" data-intro="اكتب ردك هنا وخصص الخط والحجم حسب رغبتك" data-step="3">
                    <h3 class="section-title">
                        <i class="fas fa-comment-dots"></i>
                        كتابة الرد
                    </h3>

                    <!-- Preset Templates -->
                    <div class="preset-templates" id="presetTemplates">
                        <h4 class="preset-title">
                            <i class="fas fa-templates"></i>
                            قوالب جاهزة للردود
                        </h4>
                        <div class="preset-grid">
                            <button class="preset-btn" onclick="insertPreset('شكراً لك على هذا التعليق الرائع! 🙏')">
                                <i class="fas fa-heart"></i>
                                شكر وتقدير
                            </button>
                            <button class="preset-btn" onclick="insertPreset('أتفق معك تماماً في هذا الرأي 👍')">
                                <i class="fas fa-thumbs-up"></i>
                                موافقة
                            </button>
                            <button class="preset-btn" onclick="insertPreset('هذا رأي مثير للاهتمام، شكراً للمشاركة 💭')">
                                <i class="fas fa-lightbulb"></i>
                                تفاعل إيجابي
                            </button>
                            <button class="preset-btn" onclick="insertPreset('أقدر وقتك في كتابة هذا التعليق ❤️')">
                                <i class="fas fa-clock"></i>
                                تقدير الوقت
                            </button>
                            <button class="preset-btn" onclick="insertPreset('بارك الله فيك على هذه المشاركة الطيبة 🌟')">
                                <i class="fas fa-star"></i>
                                دعاء وبركة
                            </button>
                            <button class="preset-btn" onclick="insertPreset('جزاك الله خيراً على هذا التفاعل الجميل 🤲')">
                                <i class="fas fa-hands"></i>
                                دعاء بالخير
                            </button>
                        </div>
                    </div>

                    <div class="text-input-container">
                        <div class="textarea-wrapper">
                            <textarea
                                id="replyText"
                                class="reply-textarea"
                                placeholder="اكتب ردك هنا باللغة العربية..."
                                rows="4"
                            ></textarea>
                            <div class="textarea-tools">
                                <button class="text-tool" onclick="toggleBold()" title="خط عريض (Ctrl+B)">
                                    <i class="fas fa-bold"></i>
                                </button>
                                <button class="text-tool" onclick="toggleItalic()" title="خط مائل (Ctrl+I)">
                                    <i class="fas fa-italic"></i>
                                </button>
                                <button class="text-tool" onclick="undoText()" title="تراجع (Ctrl+Z)">
                                    <i class="fas fa-undo"></i>
                                </button>
                                <button class="text-tool" onclick="redoText()" title="إعادة (Ctrl+Y)">
                                    <i class="fas fa-redo"></i>
                                </button>
                                <button class="text-tool" onclick="clearText()" title="مسح النص">
                                    <i class="fas fa-eraser"></i>
                                </button>
                            </div>
                        </div>

                        <div class="text-options">
                            <div class="font-options">
                                <label for="fontSelect">نوع الخط:</label>
                                <select id="fontSelect" class="font-select">
                                    <option value="Noto Sans Arabic">Noto Sans Arabic</option>
                                    <option value="Amiri">Amiri - خط أميري</option>
                                    <option value="Scheherazade New">Scheherazade New - شهرزاد</option>
                                    <option value="Cairo">Cairo - خط القاهرة</option>
                                    <option value="Tajawal">Tajawal - تجوال</option>
                                </select>
                            </div>
                            <div class="size-options">
                                <label for="fontSize">حجم الخط:</label>
                                <input type="range" id="fontSize" class="font-size-slider" min="16" max="40" value="22">
                                <span id="fontSizeValue">22px</span>
                            </div>
                        </div>

                        <div class="advanced-options">
                            <div class="style-options">
                                <label>تأثيرات النص:</label>
                                <div class="style-controls">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="textShadow">
                                        <span class="checkmark"></span>
                                        ظل النص
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="textOutline">
                                        <span class="checkmark"></span>
                                        حدود النص
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="textGradient">
                                        <span class="checkmark"></span>
                                        تدرج لوني
                                    </label>
                                </div>
                            </div>

                            <div class="background-options">
                                <label for="textBackground">خلفية النص:</label>
                                <select id="textBackground" class="background-select">
                                    <option value="transparent">شفاف</option>
                                    <option value="white">أبيض</option>
                                    <option value="black">أسود</option>
                                    <option value="gradient">تدرج لوني</option>
                                    <option value="custom">لون مخصص</option>
                                </select>
                                <input type="color" id="customColor" class="color-picker" value="#ffffff" style="display: none;">
                            </div>
                        </div>

                        <div class="preset-actions">
                            <button class="preset-action-btn" onclick="savePreset()">
                                <i class="fas fa-save"></i>
                                حفظ كقالب
                            </button>
                            <button class="preset-action-btn" onclick="loadPreset()">
                                <i class="fas fa-folder-open"></i>
                                تحميل قالب
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Generate Section -->
                <div class="generate-section">
                    <h3 class="section-title">
                        <i class="fas fa-magic"></i>
                        إنشاء الصورة النهائية
                    </h3>
                    <div class="generate-controls">
                        <button class="generate-button" id="generateButton" onclick="generateReplyImage()">
                            <i class="fas fa-image"></i>
                            إنشاء صورة الرد
                        </button>
                        <div class="format-options">
                            <label>
                                <input type="radio" name="format" value="square" checked>
                                مربع (1080x1080) - إنستغرام
                            </label>
                            <label>
                                <input type="radio" name="format" value="story">
                                ستوري (1080x1920) - إنستغرام ستوري
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Result Section -->
                <div class="result-section" id="resultSection" style="display: none;">
                    <h3 class="section-title">
                        <i class="fas fa-check-circle"></i>
                        النتيجة النهائية
                    </h3>
                    <div class="result-container">
                        <div class="result-preview">
                            <canvas id="resultCanvas" style="max-width: 100%; border-radius: var(--radius-medium); box-shadow: var(--shadow-medium);"></canvas>
                        </div>
                        <div class="download-controls">
                            <button class="download-button" id="downloadButton" onclick="downloadImage()">
                                <i class="fas fa-download"></i>
                                تحميل الصورة
                            </button>
                            <button class="share-button" onclick="shareImage()">
                                <i class="fas fa-share-alt"></i>
                                مشاركة
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Status Messages -->
                <div class="status-container">
                    <div class="status-message" id="statusMessage" style="display: none;"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let uploadedImage = null;
        let uploadedImageFile = null;
        let textHistory = [];
        let historyIndex = -1;
        let isTextBold = false;
        let isTextItalic = false;
        let currentImageRotation = 0;

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeEventListeners();
            updateFontSizeDisplay();
            initializeKeyboardShortcuts();
            initializeAnimations();
            loadUserPreferences();
            registerServiceWorker();
        });

        // Initialize animations on scroll
        function initializeAnimations() {
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);

            // Observe all animated elements
            document.querySelectorAll('.animate-fade-in-up, .animate-fade-in-down, .animate-fade-in-left, .animate-fade-in-right').forEach(el => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(30px)';
                observer.observe(el);
            });
        }

        // Register Service Worker for PWA
        function registerServiceWorker() {
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.register('data:application/javascript;base64,')
                    .catch(err => console.log('Service Worker registration failed'));
            }
        }

        // Load user preferences
        function loadUserPreferences() {
            const savedFont = localStorage.getItem('preferredFont');
            const savedFontSize = localStorage.getItem('preferredFontSize');

            if (savedFont) {
                document.getElementById('fontSelect').value = savedFont;
            }
            if (savedFontSize) {
                document.getElementById('fontSize').value = savedFontSize;
                updateFontSizeDisplay();
            }
        }

        // Save user preferences
        function saveUserPreferences() {
            const font = document.getElementById('fontSelect').value;
            const fontSize = document.getElementById('fontSize').value;

            localStorage.setItem('preferredFont', font);
            localStorage.setItem('preferredFontSize', fontSize);
        }

        // Initialize keyboard shortcuts
        function initializeKeyboardShortcuts() {
            document.addEventListener('keydown', function(e) {
                // Ctrl+B for bold
                if (e.ctrlKey && e.key === 'b') {
                    e.preventDefault();
                    toggleBold();
                }
                // Ctrl+I for italic
                if (e.ctrlKey && e.key === 'i') {
                    e.preventDefault();
                    toggleItalic();
                }
                // Ctrl+Z for undo
                if (e.ctrlKey && e.key === 'z') {
                    e.preventDefault();
                    undoText();
                }
                // Ctrl+Y for redo
                if (e.ctrlKey && e.key === 'y') {
                    e.preventDefault();
                    redoText();
                }
                // Ctrl+Enter to generate image
                if (e.ctrlKey && e.key === 'Enter') {
                    e.preventDefault();
                    generateReplyImage();
                }
                // Escape to close modals
                if (e.key === 'Escape') {
                    closeAllModals();
                }
            });
        }

        // Tutorial functionality
        function startTutorial() {
            if (typeof introJs !== 'undefined') {
                introJs().setOptions({
                    nextLabel: 'التالي',
                    prevLabel: 'السابق',
                    skipLabel: 'تخطي',
                    doneLabel: 'انتهاء',
                    showProgress: true,
                    showBullets: false,
                    exitOnOverlayClick: false,
                    disableInteraction: true
                }).start();
            } else {
                showStatus('جاري تحميل الجولة التعريفية...', 'info');
            }
        }

        // Show keyboard shortcuts modal
        function showKeyboardShortcuts() {
            const modal = createModal('اختصارات لوحة المفاتيح', `
                <div style="text-align: right; line-height: 2;">
                    <p><strong>Ctrl + B:</strong> خط عريض</p>
                    <p><strong>Ctrl + I:</strong> خط مائل</p>
                    <p><strong>Ctrl + Z:</strong> تراجع</p>
                    <p><strong>Ctrl + Y:</strong> إعادة</p>
                    <p><strong>Ctrl + Enter:</strong> إنشاء الصورة</p>
                    <p><strong>Escape:</strong> إغلاق النوافذ</p>
                </div>
            `);
            document.body.appendChild(modal);
        }

        // Show presets modal
        function showPresets() {
            const modal = createModal('القوالب المحفوظة', `
                <div id="savedPresets">
                    <p style="text-align: center; color: var(--text-secondary);">لا توجد قوالب محفوظة</p>
                </div>
            `);
            document.body.appendChild(modal);
            loadSavedPresets();
        }

        // Create modal helper function
        function createModal(title, content) {
            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content animate-scale-in">
                    <div class="modal-header">
                        <h3>${title}</h3>
                        <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        ${content}
                    </div>
                </div>
            `;

            // Add modal styles if not already added
            if (!document.querySelector('#modal-styles')) {
                const styles = document.createElement('style');
                styles.id = 'modal-styles';
                styles.textContent = `
                    .modal-overlay {
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background: rgba(0,0,0,0.5);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        z-index: 1000;
                        backdrop-filter: blur(5px);
                    }
                    .modal-content {
                        background: var(--white);
                        border-radius: var(--radius-large);
                        max-width: 500px;
                        width: 90%;
                        max-height: 80vh;
                        overflow-y: auto;
                        box-shadow: var(--shadow-xl);
                    }
                    .modal-header {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        padding: var(--space-4);
                        border-bottom: 1px solid var(--border-color);
                        background: var(--instagram-gradient);
                        color: var(--white);
                        border-radius: var(--radius-large) var(--radius-large) 0 0;
                    }
                    .modal-close {
                        background: none;
                        border: none;
                        color: var(--white);
                        font-size: var(--font-size-lg);
                        cursor: pointer;
                        padding: var(--space-1);
                        border-radius: var(--radius-small);
                        transition: var(--transition-fast);
                    }
                    .modal-close:hover {
                        background: rgba(255,255,255,0.2);
                    }
                    .modal-body {
                        padding: var(--space-6);
                    }
                `;
                document.head.appendChild(styles);
            }

            return modal;
        }

        // Close all modals
        function closeAllModals() {
            document.querySelectorAll('.modal-overlay').forEach(modal => modal.remove());
        }

        function initializeEventListeners() {
            // File input change event
            document.getElementById('fileInput').addEventListener('change', handleFileSelect);

            // Drag and drop events
            const uploadArea = document.getElementById('uploadArea');
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('dragleave', handleDragLeave);
            uploadArea.addEventListener('drop', handleDrop);

            // Font size slider
            document.getElementById('fontSize').addEventListener('input', updateFontSizeDisplay);

            // Prevent default drag behaviors
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                uploadArea.addEventListener(eventName, preventDefaults, false);
                document.body.addEventListener(eventName, preventDefaults, false);
            });
        }

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        function handleDragOver(e) {
            document.getElementById('uploadArea').classList.add('dragover');
        }

        function handleDragLeave(e) {
            document.getElementById('uploadArea').classList.remove('dragover');
        }

        function handleDrop(e) {
            const uploadArea = document.getElementById('uploadArea');
            uploadArea.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        }

        function handleFileSelect(e) {
            const file = e.target.files[0];
            if (file) {
                handleFile(file);
            }
        }

        function handleFile(file) {
            // Validate file type
            if (!file.type.startsWith('image/')) {
                showStatus('يرجى اختيار ملف صورة صالح', 'error');
                return;
            }

            // Validate file size (max 10MB)
            if (file.size > 10 * 1024 * 1024) {
                showStatus('حجم الملف كبير جداً. يرجى اختيار صورة أصغر من 10 ميجابايت', 'error');
                return;
            }

            uploadedImageFile = file;

            // Create FileReader to read the image
            const reader = new FileReader();
            reader.onload = function(e) {
                const img = new Image();
                img.onload = function() {
                    uploadedImage = img;
                    displayImagePreview(e.target.result);
                    updateUploadAreaState();
                    showStatus('تم رفع الصورة بنجاح!', 'success');
                };
                img.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }

        function displayImagePreview(imageSrc) {
            const previewContainer = document.getElementById('imagePreview');
            const previewImage = document.getElementById('previewImage');

            previewImage.src = imageSrc;
            previewContainer.style.display = 'block';
        }

        function updateUploadAreaState() {
            const uploadArea = document.getElementById('uploadArea');
            const uploadIcon = uploadArea.querySelector('.upload-icon i');
            const uploadText = uploadArea.querySelector('.upload-text');

            uploadArea.classList.add('has-file');
            uploadIcon.className = 'fas fa-check-circle';
            uploadText.textContent = 'تم رفع الصورة بنجاح! يمكنك اختيار صورة أخرى إذا أردت';
        }

        function updateFontSizeDisplay() {
            const fontSize = document.getElementById('fontSize').value;
            document.getElementById('fontSizeValue').textContent = fontSize + 'px';
            saveUserPreferences();
        }

        // Text editing functions
        function insertPreset(text) {
            const textarea = document.getElementById('replyText');
            const currentText = textarea.value;
            const newText = currentText ? currentText + '\n\n' + text : text;

            saveTextToHistory();
            textarea.value = newText;
            textarea.focus();

            // Animate the textarea
            textarea.style.transform = 'scale(1.02)';
            setTimeout(() => {
                textarea.style.transform = 'scale(1)';
            }, 200);
        }

        function toggleBold() {
            isTextBold = !isTextBold;
            const textarea = document.getElementById('replyText');
            const button = document.querySelector('[onclick="toggleBold()"]');

            if (isTextBold) {
                button.style.background = 'var(--primary-color)';
                button.style.color = 'var(--white)';
                textarea.style.fontWeight = 'bold';
            } else {
                button.style.background = '';
                button.style.color = '';
                textarea.style.fontWeight = 'normal';
            }
        }

        function toggleItalic() {
            isTextItalic = !isTextItalic;
            const textarea = document.getElementById('replyText');
            const button = document.querySelector('[onclick="toggleItalic()"]');

            if (isTextItalic) {
                button.style.background = 'var(--primary-color)';
                button.style.color = 'var(--white)';
                textarea.style.fontStyle = 'italic';
            } else {
                button.style.background = '';
                button.style.color = '';
                textarea.style.fontStyle = 'normal';
            }
        }

        function saveTextToHistory() {
            const textarea = document.getElementById('replyText');
            const currentText = textarea.value;

            // Remove any future history if we're not at the end
            if (historyIndex < textHistory.length - 1) {
                textHistory = textHistory.slice(0, historyIndex + 1);
            }

            textHistory.push(currentText);
            historyIndex = textHistory.length - 1;

            // Limit history to 50 entries
            if (textHistory.length > 50) {
                textHistory.shift();
                historyIndex--;
            }
        }

        function undoText() {
            if (historyIndex > 0) {
                historyIndex--;
                const textarea = document.getElementById('replyText');
                textarea.value = textHistory[historyIndex];

                // Visual feedback
                textarea.style.background = 'rgba(40, 167, 69, 0.1)';
                setTimeout(() => {
                    textarea.style.background = '';
                }, 300);
            }
        }

        function redoText() {
            if (historyIndex < textHistory.length - 1) {
                historyIndex++;
                const textarea = document.getElementById('replyText');
                textarea.value = textHistory[historyIndex];

                // Visual feedback
                textarea.style.background = 'rgba(23, 162, 184, 0.1)';
                setTimeout(() => {
                    textarea.style.background = '';
                }, 300);
            }
        }

        function clearText() {
            const textarea = document.getElementById('replyText');
            if (textarea.value.trim()) {
                saveTextToHistory();
                textarea.value = '';
                textarea.focus();

                // Visual feedback
                textarea.style.background = 'rgba(220, 53, 69, 0.1)';
                setTimeout(() => {
                    textarea.style.background = '';
                }, 300);
            }
        }

        // Image manipulation functions
        function cropImage() {
            if (!uploadedImage) return;

            showStatus('ميزة قص الصورة قيد التطوير...', 'info');
            // TODO: Implement image cropping functionality
        }

        function rotateImage() {
            if (!uploadedImage) return;

            currentImageRotation = (currentImageRotation + 90) % 360;
            const previewImage = document.getElementById('previewImage');
            previewImage.style.transform = `rotate(${currentImageRotation}deg)`;

            // Create rotated canvas
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            if (currentImageRotation === 90 || currentImageRotation === 270) {
                canvas.width = uploadedImage.height;
                canvas.height = uploadedImage.width;
            } else {
                canvas.width = uploadedImage.width;
                canvas.height = uploadedImage.height;
            }

            ctx.translate(canvas.width / 2, canvas.height / 2);
            ctx.rotate((currentImageRotation * Math.PI) / 180);
            ctx.drawImage(uploadedImage, -uploadedImage.width / 2, -uploadedImage.height / 2);

            // Update the uploaded image
            const rotatedImage = new Image();
            rotatedImage.onload = function() {
                uploadedImage = rotatedImage;
            };
            rotatedImage.src = canvas.toDataURL();

            showStatus('تم تدوير الصورة بنجاح!', 'success');
        }

        function removeImage() {
            uploadedImage = null;
            uploadedImageFile = null;
            currentImageRotation = 0;

            document.getElementById('imagePreview').style.display = 'none';
            document.getElementById('uploadArea').classList.remove('has-file');

            const uploadIcon = document.querySelector('.upload-icon i');
            const uploadText = document.querySelector('.upload-text');

            uploadIcon.className = 'fas fa-cloud-upload-alt';
            uploadText.textContent = 'اسحب وأفلت صورة التعليق هنا أو انقر للاختيار';

            showStatus('تم حذف الصورة', 'info');
        }

        // Preset management functions
        function savePreset() {
            const font = document.getElementById('fontSelect').value;
            const fontSize = document.getElementById('fontSize').value;
            const textShadow = document.getElementById('textShadow').checked;
            const textOutline = document.getElementById('textOutline').checked;
            const textGradient = document.getElementById('textGradient').checked;
            const textBackground = document.getElementById('textBackground').value;
            const customColor = document.getElementById('customColor').value;

            const preset = {
                id: Date.now(),
                name: prompt('اسم القالب:') || 'قالب جديد',
                font,
                fontSize,
                textShadow,
                textOutline,
                textGradient,
                textBackground,
                customColor,
                createdAt: new Date().toISOString()
            };

            if (preset.name) {
                const savedPresets = JSON.parse(localStorage.getItem('textPresets') || '[]');
                savedPresets.push(preset);
                localStorage.setItem('textPresets', JSON.stringify(savedPresets));

                showStatus('تم حفظ القالب بنجاح!', 'success');
            }
        }

        function loadPreset() {
            const savedPresets = JSON.parse(localStorage.getItem('textPresets') || '[]');

            if (savedPresets.length === 0) {
                showStatus('لا توجد قوالب محفوظة', 'info');
                return;
            }

            const presetList = savedPresets.map(preset =>
                `<div class="preset-item" onclick="applyPreset(${preset.id})">
                    <strong>${preset.name}</strong>
                    <small>${preset.font} - ${preset.fontSize}px</small>
                </div>`
            ).join('');

            const modal = createModal('تحميل قالب', `
                <div class="preset-list">
                    ${presetList}
                </div>
            `);

            document.body.appendChild(modal);
        }

        function loadSavedPresets() {
            const savedPresets = JSON.parse(localStorage.getItem('textPresets') || '[]');
            const container = document.getElementById('savedPresets');

            if (savedPresets.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: var(--text-secondary);">لا توجد قوالب محفوظة</p>';
                return;
            }

            container.innerHTML = savedPresets.map(preset => `
                <div class="preset-item" style="display: flex; justify-content: space-between; align-items: center; padding: var(--space-3); border: 1px solid var(--border-color); border-radius: var(--radius-medium); margin-bottom: var(--space-2);">
                    <div onclick="applyPreset(${preset.id})" style="cursor: pointer; flex: 1;">
                        <strong>${preset.name}</strong><br>
                        <small style="color: var(--text-secondary);">${preset.font} - ${preset.fontSize}px</small>
                    </div>
                    <button onclick="deletePreset(${preset.id})" style="background: var(--danger-color); color: var(--white); border: none; padding: var(--space-1); border-radius: var(--radius-small); cursor: pointer;">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `).join('');
        }

        function applyPreset(presetId) {
            const savedPresets = JSON.parse(localStorage.getItem('textPresets') || '[]');
            const preset = savedPresets.find(p => p.id === presetId);

            if (preset) {
                document.getElementById('fontSelect').value = preset.font;
                document.getElementById('fontSize').value = preset.fontSize;
                document.getElementById('textShadow').checked = preset.textShadow;
                document.getElementById('textOutline').checked = preset.textOutline;
                document.getElementById('textGradient').checked = preset.textGradient;
                document.getElementById('textBackground').value = preset.textBackground;
                document.getElementById('customColor').value = preset.customColor;

                updateFontSizeDisplay();
                closeAllModals();
                showStatus('تم تطبيق القالب بنجاح!', 'success');
            }
        }

        function deletePreset(presetId) {
            if (confirm('هل أنت متأكد من حذف هذا القالب؟')) {
                const savedPresets = JSON.parse(localStorage.getItem('textPresets') || '[]');
                const filteredPresets = savedPresets.filter(p => p.id !== presetId);
                localStorage.setItem('textPresets', JSON.stringify(filteredPresets));

                loadSavedPresets();
                showStatus('تم حذف القالب', 'info');
            }
        }

        function showStatus(message, type) {
            const statusElement = document.getElementById('statusMessage');
            statusElement.textContent = message;
            statusElement.className = `status-message ${type}`;
            statusElement.style.display = 'block';

            // Auto-hide after 5 seconds
            setTimeout(() => {
                statusElement.style.display = 'none';
            }, 5000);
        }

        async function generateReplyImage() {
            // Validate inputs
            if (!uploadedImage) {
                showStatus('يرجى رفع صورة التعليق أولاً', 'error');
                return;
            }

            const replyText = document.getElementById('replyText').value.trim();
            if (!replyText) {
                showStatus('يرجى كتابة نص الرد', 'error');
                return;
            }

            // Show loading state
            const generateButton = document.getElementById('generateButton');
            const originalText = generateButton.innerHTML;
            generateButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإنشاء...';
            generateButton.disabled = true;
            generateButton.classList.add('loading');

            try {
                await createCombinedImage();
                showStatus('تم إنشاء صورة الرد بنجاح!', 'success');
                document.getElementById('resultSection').style.display = 'block';

                // Scroll to result
                document.getElementById('resultSection').scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            } catch (error) {
                console.error('Error generating image:', error);
                showStatus('حدث خطأ في إنشاء الصورة: ' + error.message, 'error');
            } finally {
                // Reset button state
                generateButton.innerHTML = originalText;
                generateButton.disabled = false;
                generateButton.classList.remove('loading');
            }
        }

        async function createCombinedImage() {
            const canvas = document.getElementById('resultCanvas');
            const ctx = canvas.getContext('2d');

            // Get format selection
            const format = document.querySelector('input[name="format"]:checked').value;
            const canvasWidth = 1080;
            const canvasHeight = format === 'square' ? 1080 : 1920;

            // Set canvas dimensions
            canvas.width = canvasWidth;
            canvas.height = canvasHeight;

            // Clear canvas with white background
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(0, 0, canvasWidth, canvasHeight);

            // Calculate image dimensions and position
            const maxImageHeight = canvasHeight * 0.6; // 60% of canvas height for the original image
            const imageScale = Math.min(canvasWidth / uploadedImage.width, maxImageHeight / uploadedImage.height);
            const scaledWidth = uploadedImage.width * imageScale;
            const scaledHeight = uploadedImage.height * imageScale;
            const imageX = (canvasWidth - scaledWidth) / 2;
            const imageY = 40; // Top margin

            // Draw the uploaded image
            ctx.drawImage(uploadedImage, imageX, imageY, scaledWidth, scaledHeight);

            // Add a separator line
            const separatorY = imageY + scaledHeight + 30;
            ctx.strokeStyle = '#dbdbdb';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(40, separatorY);
            ctx.lineTo(canvasWidth - 40, separatorY);
            ctx.stroke();

            // Prepare text styling
            const fontSize = parseInt(document.getElementById('fontSize').value);
            const fontFamily = document.getElementById('fontSelect').value;
            const replyText = document.getElementById('replyText').value.trim();

            // Set text properties
            ctx.fillStyle = '#262626';
            ctx.font = `${fontSize}px "${fontFamily}", Arial, sans-serif`;
            ctx.textAlign = 'right';
            ctx.direction = 'rtl';

            // Calculate text area
            const textStartY = separatorY + 50;
            const textMaxWidth = canvasWidth - 80; // 40px margin on each side
            const textX = canvasWidth - 40; // Right-aligned

            // Draw reply text with word wrapping
            drawWrappedText(ctx, replyText, textX, textStartY, textMaxWidth, fontSize * 1.4);

            // Add AliToucan branding
            addBranding(ctx, canvasWidth, canvasHeight);
        }

        function drawWrappedText(ctx, text, x, y, maxWidth, lineHeight) {
            const words = text.split(' ');
            let line = '';
            let currentY = y;

            for (let i = 0; i < words.length; i++) {
                const testLine = line + words[i] + ' ';
                const metrics = ctx.measureText(testLine);
                const testWidth = metrics.width;

                if (testWidth > maxWidth && i > 0) {
                    ctx.fillText(line, x, currentY);
                    line = words[i] + ' ';
                    currentY += lineHeight;
                } else {
                    line = testLine;
                }
            }
            ctx.fillText(line, x, currentY);
        }

        function addBranding(ctx, canvasWidth, canvasHeight) {
            // Add subtle AliToucan watermark
            ctx.save();
            ctx.globalAlpha = 0.3;
            ctx.fillStyle = '#e1306c';
            ctx.font = '16px "Amiri", serif';
            ctx.textAlign = 'center';
            ctx.fillText('AliToucan', canvasWidth / 2, canvasHeight - 20);
            ctx.restore();
        }

        function downloadImage() {
            const canvas = document.getElementById('resultCanvas');

            // Create download link
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;

                // Generate Arabic filename with timestamp
                const now = new Date();
                const timestamp = now.toISOString().slice(0, 19).replace(/:/g, '-');
                link.download = `رد_انستغرام_${timestamp}.png`;

                // Trigger download
                link.style.display = 'none';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // Clean up
                setTimeout(() => {
                    URL.revokeObjectURL(url);
                }, 1000);

                showStatus('تم تحميل الصورة بنجاح!', 'success');
            }, 'image/png', 0.95);
        }

        function shareImage() {
            const canvas = document.getElementById('resultCanvas');

            if (navigator.share && navigator.canShare) {
                canvas.toBlob(async function(blob) {
                    const file = new File([blob], 'instagram_reply.png', { type: 'image/png' });

                    if (navigator.canShare({ files: [file] })) {
                        try {
                            await navigator.share({
                                title: 'رد إنستغرام',
                                text: 'صورة رد تم إنشاؤها بواسطة AliToucan',
                                files: [file]
                            });
                            showStatus('تم مشاركة الصورة بنجاح!', 'success');
                        } catch (error) {
                            console.error('Error sharing:', error);
                            fallbackShare();
                        }
                    } else {
                        fallbackShare();
                    }
                }, 'image/png');
            } else {
                fallbackShare();
            }
        }

        function fallbackShare() {
            // Fallback: copy canvas as image to clipboard if supported
            if (navigator.clipboard && window.ClipboardItem) {
                const canvas = document.getElementById('resultCanvas');
                canvas.toBlob(async function(blob) {
                    try {
                        const item = new ClipboardItem({ 'image/png': blob });
                        await navigator.clipboard.write([item]);
                        showStatus('تم نسخ الصورة إلى الحافظة!', 'success');
                    } catch (error) {
                        console.error('Error copying to clipboard:', error);
                        showStatus('لا يمكن مشاركة الصورة. يرجى استخدام زر التحميل', 'info');
                    }
                }, 'image/png');
            } else {
                showStatus('لا يمكن مشاركة الصورة. يرجى استخدام زر التحميل', 'info');
            }
        }
    </script>
</body>
</html>
